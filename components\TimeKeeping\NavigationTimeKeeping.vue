<template>
  <div class="bg-white rounded-lg border border-gray-200 h-fit">
    <div class="p-4 space-y-6">
      <!-- Custom Date Range -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3"
          >T<PERSON><PERSON> chọn thời gian</label
        >
        <div class="space-y-3">
          <div>
            <label class="block text-xs text-gray-500 mb-1">Từ ngày</label>
            <input
              v-model="filters.dateFrom"
              type="date"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm"
            />
          </div>
          <div>
            <label class="block text-xs text-gray-500 mb-1">Đ<PERSON>n ngày</label>
            <input
              v-model="filters.dateTo"
              type="date"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm"
            />
          </div>
        </div>
      </div>

      <!-- Employee Search -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3"
          >Tìm kiếm nhân viên</label
        >
        <div class="relative">
          <input
            v-model="searchEmployee"
            type="text"
            placeholder="Nhập tên hoặc mã nhân viên..."
            class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary text-sm"
            @input="handleEmployeeSearch"
          />
          <svg
            class="absolute left-3 top-2.5 w-4 h-4 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
      </div>

      <!-- Employee List -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3"
          >Danh sách nhân viên</label
        >
        <div class="max-h-64 overflow-y-auto border border-gray-200 rounded-lg">
          <div v-if="isLoadingEmployees" class="p-4 text-center">
            <div
              class="animate-spin w-5 h-5 border-2 border-primary border-t-transparent rounded-full mx-auto"
            ></div>
            <p class="text-sm text-gray-500 mt-2">Đang tải...</p>
          </div>

          <div
            v-else-if="filteredEmployees.length === 0"
            class="p-4 text-center text-gray-500 text-sm"
          >
            {{
              searchEmployee ? "Không tìm thấy nhân viên" : "Chưa có nhân viên"
            }}
          </div>

          <div v-else class="divide-y divide-gray-200">
            <label
              v-for="employee in filteredEmployees"
              :key="employee.id"
              class="flex items-center p-3 hover:bg-gray-50 cursor-pointer transition-colors"
            >
              <input
                v-model="filters.selectedEmployees"
                :value="employee.id"
                type="checkbox"
                class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary/20"
              />
              <div class="ml-3 flex items-center gap-3 flex-1">
                <img
                  :src="employee.avatar || 'https://placehold.co/32'"
                  :alt="employee.name"
                  class="w-8 h-8 rounded-full object-cover border border-gray-200"
                />
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-gray-900 truncate">
                    {{ employee.name }}
                  </p>
                  <p class="text-xs text-gray-500 truncate">
                    {{ employee.code }}
                  </p>
                </div>
                <div class="flex items-center gap-1">
                  <div
                    :class="getEmployeeStatusClass(employee.status)"
                    class="w-2 h-2 rounded-full"
                  ></div>
                  <span class="text-xs text-gray-500">{{
                    getEmployeeStatusText(employee.status)
                  }}</span>
                </div>
              </div>
            </label>
          </div>
        </div>
      </div>

      <!-- Status Filter -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3"
          >Loại chấm công</label
        >
        <div class="space-y-2">
          <label
            v-for="status in statusOptions"
            :key="status.value"
            class="flex items-center cursor-pointer"
          >
            <input
              v-model="filters.selectedStatuses"
              :value="status.value"
              type="checkbox"
              class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary/20"
            />
            <span class="ml-3 text-sm text-gray-700">{{ status.label }}</span>
            <div
              :class="status.colorClass"
              class="ml-auto px-2 py-1 rounded-full text-xs"
            >
              {{ status.label }}
            </div>
          </label>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="space-y-3 pt-4 border-t border-gray-200">
        <button
          @click="applyFilters"
          :disabled="isApplying"
          class="w-full bg-primary text-white py-2 px-4 rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
        >
          <svg
            v-if="isApplying"
            class="animate-spin w-4 h-4"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          {{ isApplying ? "Đang áp dụng..." : "Áp dụng bộ lọc" }}
        </button>

        <button
          @click="clearFilters"
          class="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors"
        >
          Xóa bộ lọc
        </button>
      </div>

      <!-- Filter Summary -->
      <div
        v-if="hasActiveFilters"
        class="bg-blue-50 border border-blue-200 rounded-lg p-3"
      >
        <h4 class="text-sm font-medium text-blue-900 mb-2">
          Bộ lọc đang áp dụng:
        </h4>
        <div class="space-y-1 text-xs text-blue-700">
          <div v-if="selectedPeriod !== 'custom'">
            <span class="font-medium">Thời gian:</span>
            {{ getSelectedPeriodLabel() }}
          </div>
          <div
            v-if="
              selectedPeriod === 'custom' &&
              (filters.dateFrom || filters.dateTo)
            "
          >
            <span class="font-medium">Thời gian:</span>
            {{ filters.dateFrom || "..." }} đến {{ filters.dateTo || "..." }}
          </div>
          <div v-if="filters.selectedEmployees.length > 0">
            <span class="font-medium">Nhân viên:</span>
            {{ filters.selectedEmployees.length }} người
          </div>
          <div v-if="filters.selectedStatuses.length > 0">
            <span class="font-medium">Loại:</span>
            {{ filters.selectedStatuses.join(", ") }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from "vue";

// Types
interface Employee {
  id: string;
  name: string;
  code: string;
  avatar?: string;
  status: "active" | "inactive" | "on_leave";
  department?: string;
}

interface QuickPeriod {
  value: string;
  label: string;
  days?: number;
}

interface StatusOption {
  value: string;
  label: string;
  colorClass: string;
}

interface Filters {
  dateFrom: string;
  dateTo: string;
  selectedEmployees: string[];
  selectedStatuses: string[];
}

// Emits
const emit = defineEmits<{
  filtersChanged: [filters: Filters & { period: string }];
  employeeSelected: [employees: Employee[]];
  dateRangeChanged: [dateFrom: string, dateTo: string];
}>();

// Reactive state
const selectedPeriod = ref<string>("today");
const searchEmployee = ref<string>("");
const isLoadingEmployees = ref<boolean>(false);
const isApplying = ref<boolean>(false);

const filters = reactive<Filters>({
  dateFrom: "",
  dateTo: "",
  selectedEmployees: [],
  selectedStatuses: [],
});

// Quick period options
const quickPeriods: QuickPeriod[] = [
  { value: "today", label: "Hôm nay" },
  { value: "yesterday", label: "Hôm qua" },
  { value: "this_week", label: "Tuần này" },
  { value: "last_week", label: "Tuần trước" },
  { value: "this_month", label: "Tháng này" },
  { value: "last_month", label: "Tháng trước" },
  { value: "last_7_days", label: "7 ngày qua" },
  { value: "custom", label: "Tùy chọn" },
];

// Status options
const statusOptions: StatusOption[] = [
  {
    value: "CHECK_IN",
    label: "Vào ca",
    colorClass: "bg-green-100 text-green-800",
  },
  {
    value: "CHECK_OUT",
    label: "Hết ca",
    colorClass: "bg-red-100 text-red-800",
  },
];

// Mock employees data
const mockEmployees: Employee[] = [
  {
    id: "1",
    name: "Nguyễn Văn A",
    code: "NV001",
    avatar: "https://placehold.co/32",
    status: "active",
    department: "Kinh doanh",
  },
  {
    id: "2",
    name: "Trần Thị B",
    code: "NV002",
    avatar: "https://placehold.co/32",
    status: "active",
    department: "Marketing",
  },
  {
    id: "3",
    name: "Lê Văn C",
    code: "NV003",
    avatar: "https://placehold.co/32",
    status: "on_leave",
    department: "IT",
  },
  {
    id: "4",
    name: "Phạm Thị D",
    code: "NV004",
    avatar: "https://placehold.co/32",
    status: "active",
    department: "Nhân sự",
  },
  {
    id: "5",
    name: "Hoàng Văn E",
    code: "NV005",
    avatar: "https://placehold.co/32",
    status: "inactive",
    department: "Kế toán",
  },
  {
    id: "6",
    name: "Vũ Thị F",
    code: "NV006",
    avatar: "https://placehold.co/32",
    status: "active",
    department: "Kinh doanh",
  },
  {
    id: "7",
    name: "Đặng Văn G",
    code: "NV007",
    avatar: "https://placehold.co/32",
    status: "active",
    department: "Vận hành",
  },
  {
    id: "8",
    name: "Bùi Thị H",
    code: "NV008",
    avatar: "https://placehold.co/32",
    status: "on_leave",
    department: "Marketing",
  },
];

const allEmployees = ref<Employee[]>(mockEmployees);

// Computed properties
const filteredEmployees = computed(() => {
  if (!searchEmployee.value) {
    return allEmployees.value;
  }

  const search = searchEmployee.value.toLowerCase();
  return allEmployees.value.filter(
    (employee) =>
      employee.name.toLowerCase().includes(search) ||
      employee.code.toLowerCase().includes(search) ||
      employee.department?.toLowerCase().includes(search)
  );
});

const hasActiveFilters = computed(() => {
  return (
    selectedPeriod.value !== "today" ||
    filters.dateFrom ||
    filters.dateTo ||
    filters.selectedEmployees.length > 0 ||
    filters.selectedStatuses.length > 0
  );
});

// Methods
const selectQuickPeriod = (period: string) => {
  selectedPeriod.value = period;

  if (period !== "custom") {
    const { dateFrom, dateTo } = getDateRangeForPeriod(period);
    filters.dateFrom = dateFrom;
    filters.dateTo = dateTo;
  }

  // Auto apply for quick periods
  if (period !== "custom") {
    applyFilters();
  }
};

const getDateRangeForPeriod = (
  period: string
): { dateFrom: string; dateTo: string } => {
  const today = new Date();
  const formatDate = (date: Date) => date.toISOString().split("T")[0];

  switch (period) {
    case "today":
      return { dateFrom: formatDate(today), dateTo: formatDate(today) };

    case "yesterday":
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      return { dateFrom: formatDate(yesterday), dateTo: formatDate(yesterday) };

    case "this_week":
      const startOfWeek = new Date(today);
      startOfWeek.setDate(today.getDate() - today.getDay() + 1);
      return { dateFrom: formatDate(startOfWeek), dateTo: formatDate(today) };

    case "last_week":
      const lastWeekStart = new Date(today);
      lastWeekStart.setDate(today.getDate() - today.getDay() - 6);
      const lastWeekEnd = new Date(today);
      lastWeekEnd.setDate(today.getDate() - today.getDay());
      return {
        dateFrom: formatDate(lastWeekStart),
        dateTo: formatDate(lastWeekEnd),
      };

    case "this_month":
      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      return { dateFrom: formatDate(startOfMonth), dateTo: formatDate(today) };

    case "last_month":
      const lastMonthStart = new Date(
        today.getFullYear(),
        today.getMonth() - 1,
        1
      );
      const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
      return {
        dateFrom: formatDate(lastMonthStart),
        dateTo: formatDate(lastMonthEnd),
      };

    case "last_7_days":
      const sevenDaysAgo = new Date(today);
      sevenDaysAgo.setDate(today.getDate() - 7);
      return { dateFrom: formatDate(sevenDaysAgo), dateTo: formatDate(today) };

    default:
      return { dateFrom: "", dateTo: "" };
  }
};

const handleEmployeeSearch = () => {
  // Debounce search if needed
  // For now, just trigger reactive update
};

const getEmployeeStatusClass = (status: string): string => {
  switch (status) {
    case "active":
      return "bg-green-500";
    case "inactive":
      return "bg-gray-400";
    case "on_leave":
      return "bg-yellow-500";
    default:
      return "bg-gray-400";
  }
};

const getEmployeeStatusText = (status: string): string => {
  switch (status) {
    case "active":
      return "Hoạt động";
    case "inactive":
      return "Không hoạt động";
    case "on_leave":
      return "Nghỉ phép";
    default:
      return "Không xác định";
  }
};

const getSelectedPeriodLabel = (): string => {
  const period = quickPeriods.find((p) => p.value === selectedPeriod.value);
  return period?.label || "";
};

const applyFilters = async () => {
  isApplying.value = true;

  try {
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 500));

    const filterData = {
      ...filters,
      period: selectedPeriod.value,
    };

    emit("filtersChanged", filterData);
    emit("dateRangeChanged", filters.dateFrom, filters.dateTo);

    if (filters.selectedEmployees.length > 0) {
      const selectedEmployeeObjects = allEmployees.value.filter((emp) =>
        filters.selectedEmployees.includes(emp.id)
      );
      emit("employeeSelected", selectedEmployeeObjects);
    }
  } catch (error) {
    console.error("Error applying filters:", error);
  } finally {
    isApplying.value = false;
  }
};

const clearFilters = () => {
  selectedPeriod.value = "today";
  filters.dateFrom = "";
  filters.dateTo = "";
  filters.selectedEmployees = [];
  filters.selectedStatuses = [];
  searchEmployee.value = "";

  // Auto apply after clearing
  applyFilters();
};

// Initialize with today's data
onMounted(() => {
  selectQuickPeriod("today");
});

// Watch for date changes to update period
watch([() => filters.dateFrom, () => filters.dateTo], () => {
  if (filters.dateFrom || filters.dateTo) {
    selectedPeriod.value = "custom";
  }
});
</script>
