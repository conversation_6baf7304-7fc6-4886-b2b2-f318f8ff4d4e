<template>
  <div class="m-2">
    <div class="grid grid-cols-1 md:grid-cols-12 gap-2">
      <div class="col-span-12 md:col-span-3">
        <NavigationTimeKeeping></NavigationTimeKeeping>
      </div>
      <div class="col-span-12 md:col-span-9">
        <TabTimeKeeping></TabTimeKeeping>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
useHead({
  title: "Chấm công",
  meta: [
    {
      name: "description",
      content: "timekeeping",
    },
  ],
});

definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  permission: [
    "SALE_OP",
    "SUPP_ADMIN",
    "ORG_ADMIN",
    "SALE_MANAGER",
    "SUPP_OP",
    "SALE",
    "SALES",
  ],
  name: "<PERSON>ấm công",
});
</script>
